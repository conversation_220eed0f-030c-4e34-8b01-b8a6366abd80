// --- Begin lib/types.ts ---

// Enum definitions based on SQL ENUM types
export enum CostEnum {
  Cheap = "€",
  Medium = "€€",
  Expensive = "€€€",
}

export enum EffortEnum {
  Low = "1",
  Medium = "2",
  High = "3",
}

export enum ParticipantRole {
  Organizer = "organizer",
  Guest = "guest",
}

export enum ParticipantStatus {
  Pending = "pending",
  Accepted = "accepted",
  Declined = "declined",
  Maybe = "maybe",
}

// Interface for the 'profiles' table
export interface Profile {
  id: string; // UUID PRIMARY KEY REFERENCES auth.users(id)
  name: string | null;
  avatar_url: string | null;
  is_discoverable?: boolean; // Added
  updated_at: string; // TIMESTAMPTZ
  created_at: string; // TIMESTAMPTZ
}

// Interface for the 'events' table
export interface Event {
  id: number; // BIGINT PRIMARY KEY
  organizer_id: string; // UUID NOT NULL REFERENCES profiles(id)
  icon: string | null;
  title: string; // NOT NULL
  description: string | null;
  date_time: string; // TIMESTAMPTZ NOT NULL
  location: string | null;
  allow_suggestions: boolean; // DEFAULT FALSE NOT NULL
  allow_pre_assignment: boolean; // DEFAULT FALSE NOT NULL ('Fixer')
  organizer_delegated: boolean; // DEFAULT FALSE NOT NULL
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ
}

// Interface for the 'participants' table
export interface Participant {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  user_id: string | null; // UUID NULL REFERENCES profiles(id)
  anonymous_name: string | null; // TEXT
  anonymous_email: string | null; // TEXT
  anonymous_phone: string | null; // TEXT
  role: ParticipantRole; // participant_role NOT NULL DEFAULT 'guest'
  status: ParticipantStatus; // participant_status NOT NULL DEFAULT 'pending'
  invitation_token: string | null; // TEXT UNIQUE
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching (like profile name/avatar)
  profiles?: Pick<Profile, "name" | "avatar_url"> | null; // For joined data
  name_display?: string; // Helper to display name (either profile name or anonymous name)
}

// Interface for the 'items' table
export interface Item {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  suggester_id: string | null; // UUID NULL REFERENCES profiles(id)
  name: string; // TEXT NOT NULL
  category: string | null;
  estimated_cost: CostEnum | null; // cost_enum
  estimated_effort: EffortEnum | null; // effort_enum
  is_suggestion: boolean; // DEFAULT FALSE NOT NULL
  is_personal: boolean; // DEFAULT FALSE NOT NULL
  assigned_participant_id: number | null; // BIGINT NULL REFERENCES participants(id)
  fixed_by_participant_id: number | null; // BIGINT NULL REFERENCES participants(id)
  completed: boolean; // DEFAULT FALSE NOT NULL
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching
  assigned_participant?: Participant | null; // For joined data
  fixed_by_participant?: Participant | null; // For joined data
  suggester?: Profile | null; // For joined data
  event?: Event | null; // Correction: Ajout de l'événement lié possiblement joint
}

// Interface for the 'contacts' table
export interface Contact {
  id: number; // BIGINT PRIMARY KEY
  user_id: string; // UUID NOT NULL REFERENCES profiles(id)
  contact_profile_id: string | null; // UUID NULL REFERENCES profiles(id)
  name: string; // TEXT NOT NULL
  email: string | null;
  phone: string | null;
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching
  contact_profile?: Pick<Profile, "name" | "avatar_url"> | null; // For joined data
}

// Interface for the 'contact_groups' table
export interface ContactGroup {
  id: number; // BIGINT PRIMARY KEY
  user_id: string; // UUID NOT NULL REFERENCES profiles(id)
  name: string; // TEXT NOT NULL
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ
}

// Interface for the 'contact_group_members' table
export interface ContactGroupMember {
  group_id: number; // BIGINT NOT NULL REFERENCES contact_groups(id)
  contact_id: number; // BIGINT NOT NULL REFERENCES contacts(id)
}

// Interface for the 'messages' table
export interface Message {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  sender_id: string; // UUID NOT NULL REFERENCES profiles(id)
  content: string; // TEXT NOT NULL
  created_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching
  sender?: Pick<Profile, "name" | "avatar_url"> | null; // For joined data
}

// Helper type for combined Event data with participant info
export interface EventWithParticipantStatus extends Event {
  participant_status?: ParticipantStatus | null;
  participant_role?: ParticipantRole | null;
  is_organizer: boolean;
}

// --- End lib/types.ts ---
