// --- Begin lib/supabase.ts ---
import { AppState, Platform } from "react-native";
import "react-native-url-polyfill/auto";
import { createClient, SupabaseClient } from "@supabase/supabase-js"; // Import SupabaseClient
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Database } from "./database.types"; // Import generated types

// Charger les variables d'environnement via le mécanisme intégré d'Expo
const supabaseUrl =
  process.env.EXPO_PUBLIC_SUPABASE_URL ||
  "https://your-supabase-url.supabase.co";
const supabaseAnonKey =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "your-anon-key";
const supabaseServiceKey =
  process.env.EXPO_PUBLIC_SUPABASE_SERVICE_KEY || "your-service-key";

// Vérification que les clés sont bien chargées
if (supabaseUrl === "https://your-supabase-url.supabase.co") {
  console.warn(
    "Using default Supabase URL. Set EXPO_PUBLIC_SUPABASE_URL in your .env file for production."
  );
}
if (supabaseAnonKey === "your-anon-key") {
  console.warn(
    "Using default Supabase Anon Key. Set EXPO_PUBLIC_SUPABASE_ANON_KEY in your .env file for production."
  );
}

// Vrai singleton pattern pour éviter les instances multiples
class SupabaseSingleton {
  private static instance: SupabaseSingleton;
  private _supabase: SupabaseClient<Database> | null = null;
  private _supabaseAdmin: SupabaseClient<Database> | null = null;

  private constructor() {}

  public static getInstance(): SupabaseSingleton {
    if (!SupabaseSingleton.instance) {
      SupabaseSingleton.instance = new SupabaseSingleton();
    }
    return SupabaseSingleton.instance;
  }

  public get supabase(): SupabaseClient<Database> {
    if (!this._supabase) {
      this._supabase = this.createSupabaseClient();
    }
    return this._supabase;
  }

  public get supabaseAdmin(): SupabaseClient<Database> {
    if (!this._supabaseAdmin) {
      this._supabaseAdmin = this.createSupabaseAdminClient();
    }
    return this._supabaseAdmin;
  }

  private createSupabaseClient(): SupabaseClient<Database> {
    if (Platform.OS !== "web") {
      return createClient<Database>(supabaseUrl, supabaseAnonKey, {
        auth: {
          storage: AsyncStorage,
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
      });
    } else {
      return createClient<Database>(supabaseUrl, supabaseAnonKey, {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
      });
    }
  }

  private createSupabaseAdminClient(): SupabaseClient<Database> {
    return createClient<Database>(supabaseUrl, supabaseServiceKey, {
      auth: {
        persistSession: false,
      },
    });
  }
}

// Exporter les instances via le singleton
const supabaseInstance = SupabaseSingleton.getInstance();
const supabase = supabaseInstance.supabase;
const supabaseAdmin = supabaseInstance.supabaseAdmin;

export { supabase, supabaseAdmin };

// Tells Supabase Auth to continuously refresh the session automatically
// if the app is in the foreground. When this is added, you will continue
// to receive `onAuthStateChange` events with the `TOKEN_REFRESHED` or
// `SIGNED_OUT` event if the user's session is terminated. This should
// only be registered once.
if (Platform.OS !== "web") {
  AppState.addEventListener("change", (state) => {
    if (state === "active") {
      supabase.auth.startAutoRefresh();
    } else {
      supabase.auth.stopAutoRefresh();
    }
  });
}
// --- End lib/supabase.ts ---
