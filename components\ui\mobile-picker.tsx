import React, { useState } from 'react';
import { View, Modal, Pressable, Platform } from 'react-native';
import { Text } from '~/components/ui/text';
import { CustomButton as Button } from '~/components/ui/custom-button';
import { cn } from '~/lib/utils';

export interface PickerOption {
  label: string;
  value: string;
}

interface MobilePickerProps {
  options: PickerOption[];
  value?: PickerOption | null;
  onValueChange: (option: PickerOption | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function MobilePicker({
  options,
  value,
  onValueChange,
  placeholder = "Sélectionner une option",
  className,
  disabled = false,
}: MobilePickerProps) {
  const [modalVisible, setModalVisible] = useState(false);

  const handleSelect = (option: PickerOption) => {
    onValueChange(option);
    setModalVisible(false);
  };

  const handleClear = () => {
    onValueChange(null);
    setModalVisible(false);
  };

  return (
    <>
      {/* Trigger Button */}
      <Pressable
        onPress={() => !disabled && setModalVisible(true)}
        className={cn(
          "flex flex-row h-10 native:h-12 items-center justify-between rounded-md border border-input bg-background px-3 py-2",
          disabled && "opacity-50",
          className
        )}
        disabled={disabled}
      >
        <Text className={cn(
          "text-sm native:text-base",
          value ? "text-foreground" : "text-muted-foreground"
        )}>
          {value ? value.label : placeholder}
        </Text>
        <Text className="text-foreground opacity-50 text-sm">▼</Text>
      </Pressable>

      {/* Modal with options */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View className="flex-1 justify-end bg-black/50">
          <View className="bg-white rounded-t-lg max-h-96">
            {/* Header */}
            <View className="flex-row justify-between items-center p-4 border-b border-gray-200">
              <Text className="text-lg font-semibold">Sélectionner</Text>
              <Button variant="ghost" size="sm" onPress={() => setModalVisible(false)}>
                <Text>✕</Text>
              </Button>
            </View>

            {/* Options */}
            <View className="max-h-80">
              {/* Clear option */}
              {value && (
                <Pressable
                  onPress={handleClear}
                  className="p-4 border-b border-gray-100 active:bg-gray-50"
                >
                  <Text className="text-red-600 font-medium">Effacer la sélection</Text>
                </Pressable>
              )}

              {/* Options list */}
              {options.map((option) => (
                <Pressable
                  key={option.value}
                  onPress={() => handleSelect(option)}
                  className={cn(
                    "p-4 border-b border-gray-100 active:bg-gray-50 flex-row justify-between items-center",
                    value?.value === option.value && "bg-blue-50"
                  )}
                >
                  <Text className={cn(
                    "text-base",
                    value?.value === option.value ? "text-blue-600 font-medium" : "text-gray-900"
                  )}>
                    {option.label}
                  </Text>
                  {value?.value === option.value && (
                    <Text className="text-blue-600">✓</Text>
                  )}
                </Pressable>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}
