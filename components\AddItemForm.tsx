import React, { useState, useEffect } from "react";
import { View, ActivityIndicator, Alert } from "react-native";
import { Text } from "~/components/ui/text";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { CustomButton as Button } from "~/components/ui/custom-button";
import { MobilePicker, type PickerOption } from "~/components/ui/mobile-picker";
// Use generated types directly
import { Database, Constants } from "~/lib/database.types"; // Import Constants too
import { createItem, updateItem, ItemInsert } from "~/lib/supabaseCrud";
import { showToast } from "~/lib/toastService";

// Define aliases for generated types used here
type BaseItem = Database["public"]["Tables"]["items"]["Row"];
type CostEnumUnion = Database["public"]["Enums"]["cost_enum"];
type EffortEnumUnion = Database["public"]["Enums"]["effort_enum"];

interface AddItemFormProps {
  eventId: number;
  itemToEdit?: BaseItem | null; // Use BaseItem type from generated types
  onClose: () => void;
  onItemSaved: () => void;
}

// Helper for enum options using generated types and runtime Constants
const costOptions: PickerOption[] = Constants.public.Enums.cost_enum.map(
  (value) => ({
    label: value, // Display €, €€, €€€
    value: value,
  })
);
const effortOptions: PickerOption[] = Constants.public.Enums.effort_enum.map(
  (value) => ({
    label:
      value === "1" ? "1 (Faible)" : value === "2" ? "2 (Moyen)" : "3 (Élevé)", // Keep descriptive label
    value: value,
  })
);

// Predefined categories
const predefinedCategories = [
  "Nourriture",
  "Boissons",
  "Matériel",
  "Décoration",
  "Animation",
  "Autre",
];

const categoryOptions: PickerOption[] = predefinedCategories.map((cat) => ({
  label: cat,
  value: cat,
}));

export function AddItemForm({
  eventId,
  itemToEdit,
  onClose,
  onItemSaved,
}: AddItemFormProps) {
  const [name, setName] = useState("");
  const [categoryOption, setCategoryOption] = useState<PickerOption | null>(
    null
  );
  const [customCategory, setCustomCategory] = useState("");
  const [costOption, setCostOption] = useState<PickerOption | null>(null);
  const [effortOption, setEffortOption] = useState<PickerOption | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ name?: string }>({});

  const isEditMode = !!itemToEdit;

  useEffect(() => {
    if (isEditMode && itemToEdit) {
      setName(itemToEdit.name);
      // Pre-fill category
      if (itemToEdit.category) {
        const foundCategory = predefinedCategories.includes(itemToEdit.category)
          ? itemToEdit.category
          : "Autre";
        setCategoryOption({ label: foundCategory, value: foundCategory });
        setCustomCategory(foundCategory === "Autre" ? itemToEdit.category : "");
      } else {
        setCategoryOption(null);
        setCustomCategory("");
      }
      // Pre-fill cost using generated enum values
      setCostOption(
        itemToEdit.estimated_cost
          ? costOptions.find((o) => o.value === itemToEdit.estimated_cost) ||
              null
          : null
      );
      // Pre-fill effort using generated enum values
      setEffortOption(
        itemToEdit.estimated_effort
          ? effortOptions.find(
              (o) => o.value === itemToEdit.estimated_effort
            ) || null
          : null
      );
    } else {
      // Reset fields for add mode
      setName("");
      setCategoryOption(null);
      setCustomCategory("");
      setCostOption(null);
      setEffortOption(null);
      setErrors({});
    }
  }, [isEditMode, itemToEdit]);

  const validateForm = () => {
    const newErrors: { name?: string } = {};
    if (!name.trim()) {
      newErrors.name = "Le nom de l'item est obligatoire.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert("Formulaire incomplet", "Veuillez corriger les erreurs.");
      return;
    }

    setLoading(true);
    const finalCategoryValue = categoryOption?.value;
    const finalCategory =
      finalCategoryValue === "Autre"
        ? customCategory.trim()
        : finalCategoryValue;

    // Use generated types for data payload
    const commonData = {
      name: name.trim(),
      category: finalCategory || null,
      estimated_cost: costOption?.value as CostEnumUnion | null, // Use generated enum type
      estimated_effort: effortOption?.value as EffortEnumUnion | null, // Use generated enum type
    };

    try {
      if (isEditMode && itemToEdit) {
        const updated = await updateItem(itemToEdit.id, commonData);
        if (updated) {
          showToast("Item modifié avec succès !", { type: "success" });
          onItemSaved();
          onClose();
        } else {
          throw new Error("Échec de la modification de l'item");
        }
      } else {
        const itemDataForCreate: ItemInsert = {
          ...commonData,
          event_id: eventId,
          is_suggestion: false,
          is_personal: false,
          suggester_id: null,
          assigned_participant_id: null,
          fixed_by_participant_id: null,
        };
        const newItem = await createItem(itemDataForCreate);
        if (newItem) {
          showToast("Item ajouté avec succès !", { type: "success" });
          onItemSaved();
          onClose();
        } else {
          throw new Error("Échec de la création de l'item");
        }
      }
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "creating"} item:`,
        error
      );
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      showToast(`Erreur: ${errorMessage}`, { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="bg-white w-full">
      {/* Header */}
      <View className="flex-row justify-between items-center p-4 border-b border-gray-200">
        <Text className="text-xl font-semibold">
          {isEditMode ? "Modifier l'Item" : "Ajouter un Item"}
        </Text>
        <Button variant="ghost" size="sm" onPress={onClose}>
          <Text className="text-lg">✕</Text>
        </Button>
      </View>

      {/* Form Content */}
      <View className="p-4">
        <View className="mb-4">
          <Label nativeID="itemName">
            Nom de l'item <Text className="text-destructive">*</Text>
          </Label>
          <Input
            nativeID="itemName"
            placeholder="Ex: Salade composée"
            value={name}
            onChangeText={setName}
            className={errors.name ? "border-destructive" : ""}
          />
          {errors.name && (
            <Text className="text-destructive text-sm mt-1">{errors.name}</Text>
          )}
        </View>

        <View className="mb-4">
          <Label nativeID="itemCategory">Catégorie</Label>
          <MobilePicker
            options={categoryOptions}
            value={categoryOption}
            onValueChange={setCategoryOption}
            placeholder="Sélectionner une catégorie"
          />
          {categoryOption && categoryOption.value === "Autre" && (
            <Input
              placeholder="Préciser la catégorie personnalisée"
              value={customCategory}
              onChangeText={setCustomCategory}
              className="mt-2"
            />
          )}
        </View>

        <View className="mb-4">
          <Label nativeID="itemCost">Coût Estimé</Label>
          <MobilePicker
            options={costOptions}
            value={costOption}
            onValueChange={setCostOption}
            placeholder="Sélectionner un coût"
          />
        </View>

        <View className="mb-6">
          <Label nativeID="itemEffort">Effort Estimé</Label>
          <MobilePicker
            options={effortOptions}
            value={effortOption}
            onValueChange={setEffortOption}
            placeholder="Sélectionner un effort"
          />
        </View>

        <View className="flex-row justify-end gap-3">
          <Button variant="outline" onPress={onClose} disabled={loading}>
            <Text>Annuler</Text>
          </Button>
          <Button onPress={handleSubmit} disabled={loading} loading={loading}>
            {loading ? (
              <ActivityIndicator color="white" className="mr-2" />
            ) : null}
            <Text>
              {isEditMode ? "Enregistrer les Modifications" : "Ajouter l'Item"}
            </Text>
          </Button>
        </View>
      </View>
    </View>
  );
}
