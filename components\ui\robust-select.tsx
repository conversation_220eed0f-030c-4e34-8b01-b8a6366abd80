import React, { useState } from "react";
import {
  View,
  Modal,
  Pressable,
  Platform,
  Dimensions,
  ScrollView,
} from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";

export interface SelectOption {
  label: string;
  value: string;
}

interface RobustSelectProps {
  options: SelectOption[];
  value: SelectOption | null;
  onValueChange: (option: SelectOption | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  label?: string;
  error?: string;
}

export function RobustSelect({
  options,
  value,
  onValueChange,
  placeholder = "Sélectionner une option",
  className,
  disabled = false,
  label,
  error,
}: RobustSelectProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const { width: screenWidth } = Dimensions.get("window");
  const isDesktop = Platform.OS === "web" && screenWidth > 768;

  const handleSelect = (option: SelectOption) => {
    onValueChange(option);
    setModalVisible(false);
  };

  const handleClear = () => {
    onValueChange(null);
    setModalVisible(false);
  };

  // SOLUTION WEB : Modal plein écran avec z-index maximum
  const renderWebModal = () => (
    <Modal
      visible={modalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setModalVisible(false)}
    >
      {/* Overlay avec z-index maximum */}
      <View
        className="flex-1 bg-black/50 justify-center items-center"
        style={{
          zIndex: 999999999,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
      >
        <Pressable
          className="flex-1 w-full"
          onPress={() => setModalVisible(false)}
        >
          <View className="flex-1 justify-center items-center p-4">
            <Pressable onPress={(e) => e.stopPropagation()}>
              <View className="bg-white rounded-lg shadow-2xl max-w-md w-full max-h-96">
                {/* Header */}
                <View className="flex-row justify-between items-center p-4 border-b border-border">
                  <Text className="text-lg font-semibold text-foreground">
                    {label || "Sélectionner"}
                  </Text>
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => setModalVisible(false)}
                  >
                    <Text className="text-xl text-muted-foreground">✕</Text>
                  </Button>
                </View>

                {/* Options */}
                <ScrollView className="max-h-80">
                  {value && (
                    <Pressable
                      onPress={handleClear}
                      className="p-4 border-b border-border/50 active:bg-destructive/10"
                    >
                      <Text className="text-destructive font-medium text-center">
                        Effacer la sélection
                      </Text>
                    </Pressable>
                  )}

                  {options.map((option) => (
                    <Pressable
                      key={option.value}
                      onPress={() => handleSelect(option)}
                      className={cn(
                        "p-4 border-b border-border/50 active:bg-primary/10 flex-row justify-between items-center",
                        value?.value === option.value && "bg-primary/5"
                      )}
                    >
                      <Text
                        className={cn(
                          "text-base",
                          value?.value === option.value
                            ? "text-primary font-semibold"
                            : "text-foreground"
                        )}
                      >
                        {option.label}
                      </Text>
                      {value?.value === option.value && (
                        <Text className="text-primary text-lg">✓</Text>
                      )}
                    </Pressable>
                  ))}
                </ScrollView>
              </View>
            </Pressable>
          </View>
        </Pressable>
      </View>
    </Modal>
  );

  // SOLUTION MOBILE : Bottom sheet
  const renderMobileModal = () => (
    <Modal
      visible={modalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setModalVisible(false)}
    >
      <Pressable
        className="flex-1 bg-black/50 justify-end"
        onPress={() => setModalVisible(false)}
      >
        <View className="bg-white rounded-t-2xl max-h-[70%] shadow-2xl">
          <Pressable onPress={(e) => e.stopPropagation()}>
            {/* Header */}
            <View className="flex-row justify-between items-center p-6 border-b border-border">
              <Text className="text-lg font-semibold text-foreground">
                {label || "Sélectionner"}
              </Text>
              <Button
                variant="ghost"
                size="sm"
                onPress={() => setModalVisible(false)}
              >
                <Text className="text-xl text-muted-foreground">✕</Text>
              </Button>
            </View>

            {/* Options */}
            <ScrollView className="max-h-80">
              {value && (
                <Pressable
                  onPress={handleClear}
                  className="p-4 border-b border-border/50 active:bg-destructive/10"
                >
                  <Text className="text-destructive font-medium text-center">
                    Effacer la sélection
                  </Text>
                </Pressable>
              )}

              {options.map((option) => (
                <Pressable
                  key={option.value}
                  onPress={() => handleSelect(option)}
                  className={cn(
                    "p-4 border-b border-border/50 active:bg-primary/10 flex-row justify-between items-center",
                    value?.value === option.value && "bg-primary/5"
                  )}
                >
                  <Text
                    className={cn(
                      "text-base",
                      value?.value === option.value
                        ? "text-primary font-semibold"
                        : "text-foreground"
                    )}
                  >
                    {option.label}
                  </Text>
                  {value?.value === option.value && (
                    <Text className="text-primary text-lg">✓</Text>
                  )}
                </Pressable>
              ))}
            </ScrollView>
          </Pressable>
        </View>
      </Pressable>
    </Modal>
  );

  return (
    <View className="relative">
      {/* Trigger Button */}
      <Pressable
        onPress={() => !disabled && setModalVisible(true)}
        className={cn(
          "flex flex-row h-12 items-center justify-between rounded-md border bg-background px-3 py-2 text-sm ring-offset-background",
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          error
            ? "border-destructive focus:ring-destructive"
            : value
            ? "border-primary focus:ring-primary"
            : "border-input hover:border-ring",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={disabled}
        style={({ pressed }) => ({
          transform: [{ scale: pressed ? 0.98 : 1 }],
        })}
      >
        <Text
          className={cn(
            "text-sm flex-1",
            value ? "text-foreground font-medium" : "text-muted-foreground"
          )}
        >
          {value ? value.label : placeholder}
        </Text>
        <View className="ml-3">
          <Text className="text-muted-foreground text-sm">▼</Text>
        </View>
      </Pressable>

      {/* Error Message */}
      {error && (
        <Text className="text-destructive text-sm mt-1 ml-1">{error}</Text>
      )}

      {/* Modal - Platform specific */}
      {Platform.OS === "web" ? renderWebModal() : renderMobileModal()}
    </View>
  );
}
