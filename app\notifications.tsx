import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { useAuth } from "~/lib/AuthContext";
import { fetchEventsForUser } from "~/lib/supabaseCrud";
import { showToast } from "~/lib/toastService";
import { EventWithParticipantStatus } from "~/lib/types";

interface NotificationItem {
  id: string;
  type: "event_soon" | "task_assigned" | "invitation_pending";
  title: string;
  description: string;
  timestamp: Date;
  actionUrl?: string;
  actionText?: string;
  icon: string;
}

export default function NotificationsScreen() {
  const { session } = useAuth();
  const router = useRouter();
  
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [loading, setLoading] = useState(true);

  const generateNotifications = async () => {
    if (!session?.user?.id) {
      setNotifications([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Récupérer les événements de l'utilisateur
      const events = await fetchEventsForUser(session.user.id);
      const now = new Date();
      const notifications: NotificationItem[] = [];

      // Générer des notifications basées sur les événements
      events.forEach((event) => {
        const eventDate = new Date(event.date_time);
        const timeDiff = eventDate.getTime() - now.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        // Événement dans les 7 prochains jours
        if (daysDiff > 0 && daysDiff <= 7) {
          notifications.push({
            id: `event_soon_${event.id}`,
            type: "event_soon",
            title: `${event.title} approche !`,
            description: `Votre événement aura lieu ${daysDiff === 1 ? "demain" : `dans ${daysDiff} jours`}`,
            timestamp: eventDate,
            actionUrl: `/event/${event.id}`,
            actionText: "Voir l'événement",
            icon: event.icon || "🎉",
          });
        }

        // Événement aujourd'hui
        if (daysDiff === 0) {
          notifications.push({
            id: `event_today_${event.id}`,
            type: "event_soon",
            title: `${event.title} c'est aujourd'hui !`,
            description: `Votre événement commence à ${eventDate.toLocaleTimeString("fr-FR", {
              hour: "2-digit",
              minute: "2-digit",
            })}`,
            timestamp: eventDate,
            actionUrl: `/event/${event.id}`,
            actionText: "Voir l'événement",
            icon: event.icon || "🎉",
          });
        }
      });

      // Trier par date (plus récent en premier)
      notifications.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      
      setNotifications(notifications);
    } catch (error) {
      console.error("Error generating notifications:", error);
      showToast("Erreur lors du chargement des notifications.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    generateNotifications();
  }, [session]);

  const handleNotificationAction = (notification: NotificationItem) => {
    if (notification.actionUrl) {
      router.push(notification.actionUrl as any);
    }
  };

  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `dans ${Math.abs(diffDays)} jour${Math.abs(diffDays) > 1 ? "s" : ""}`;
    } else if (diffHours > 0) {
      return `dans ${Math.abs(diffHours)} heure${Math.abs(diffHours) > 1 ? "s" : ""}`;
    } else if (diffMinutes > 0) {
      return `dans ${Math.abs(diffMinutes)} minute${Math.abs(diffMinutes) > 1 ? "s" : ""}`;
    } else {
      return "maintenant";
    }
  };

  if (!session) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-center text-muted-foreground">
          Connectez-vous pour voir vos notifications.
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement des notifications...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: "#f9fafb" }}
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        {/* Header */}
        <View className="mb-6">
          <Text className="text-2xl font-bold text-center mb-2">
            Notifications
          </Text>
          <Text className="text-gray-500 text-center">
            Restez informé de vos événements
          </Text>
        </View>

        {notifications.length === 0 ? (
          <Card>
            <CardContent className="py-12">
              <View className="items-center">
                <Text className="text-6xl mb-4">🔔</Text>
                <Text className="text-xl font-semibold mb-2">
                  Aucune notification
                </Text>
                <Text className="text-muted-foreground text-center">
                  Vous êtes à jour ! Vos notifications d'événements apparaîtront ici.
                </Text>
              </View>
            </CardContent>
          </Card>
        ) : (
          <View className="gap-3">
            {notifications.map((notification) => (
              <Card key={notification.id} className="border border-border">
                <CardContent className="p-4">
                  <View className="flex-row items-start gap-3">
                    <Text className="text-2xl">{notification.icon}</Text>
                    <View className="flex-1">
                      <Text className="font-semibold text-base mb-1">
                        {notification.title}
                      </Text>
                      <Text className="text-muted-foreground text-sm mb-2">
                        {notification.description}
                      </Text>
                      <Text className="text-xs text-muted-foreground">
                        {getTimeAgo(notification.timestamp)}
                      </Text>
                    </View>
                    {notification.actionUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        onPress={() => handleNotificationAction(notification)}
                      >
                        <Text className="text-xs">
                          {notification.actionText || "Voir"}
                        </Text>
                      </Button>
                    )}
                  </View>
                </CardContent>
              </Card>
            ))}
          </View>
        )}

        {/* Actions rapides */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-lg">Actions rapides</CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            <Button
              variant="outline"
              onPress={() => router.push("/event/create")}
              className="w-full"
            >
              <Text>➕ Créer un nouvel événement</Text>
            </Button>
            <Button
              variant="outline"
              onPress={() => router.push("/(tabs)")}
              className="w-full"
            >
              <Text>📅 Voir mes événements</Text>
            </Button>
            <Button
              variant="outline"
              onPress={() => router.push("/(tabs)/tasks")}
              className="w-full"
            >
              <Text>📝 Voir mes tâches</Text>
            </Button>
          </CardContent>
        </Card>

        {/* Bouton retour */}
        <Button
          variant="outline"
          onPress={() => router.back()}
          className="mt-6 w-full"
        >
          <Text>Retour</Text>
        </Button>
      </View>
    </ScrollView>
  );
}
