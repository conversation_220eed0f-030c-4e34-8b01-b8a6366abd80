import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  fetchEventDetails,
  fetchParticipantsForEvent,
  createParticipant,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { Database } from "~/lib/database.types";

type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];

export default function ParticipantsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();
  
  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteLoading, setInviteLoading] = useState(false);

  const loadData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);
      const [eventDetails, eventParticipants] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
      ]);

      setEvent(eventDetails);
      setParticipants(eventParticipants || []);
    } catch (error) {
      console.error("Error loading data:", error);
      showToast("Erreur lors du chargement.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [id]);

  const handleInviteParticipant = async () => {
    if (!event || !session?.user?.id || !inviteEmail.trim()) return;

    // Validation email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(inviteEmail.trim())) {
      showToast("Veuillez entrer une adresse email valide.", { type: "error" });
      return;
    }

    try {
      setInviteLoading(true);
      
      // Créer un participant avec email anonyme
      const participantData = {
        event_id: event.id,
        user_id: null, // Participant anonyme pour l'instant
        role: "guest" as const,
        status: "pending" as ParticipantStatusEnum,
        anonymous_name: null,
        anonymous_email: inviteEmail.trim().toLowerCase(),
        anonymous_phone: null,
        invitation_token: crypto.randomUUID(), // Token unique pour l'invitation
      };

      const result = await createParticipant(participantData);
      
      if (result) {
        showToast("Invitation envoyée !", { type: "success" });
        setInviteEmail("");
        // Recharger la liste des participants
        loadData();
      } else {
        throw new Error("Échec de l'invitation");
      }
    } catch (error) {
      console.error("Error inviting participant:", error);
      showToast("Erreur lors de l'invitation.", { type: "error" });
    } finally {
      setInviteLoading(false);
    }
  };

  const getStatusEmoji = (status: ParticipantStatusEnum) => {
    switch (status) {
      case "accepted": return "✅";
      case "declined": return "❌";
      case "maybe": return "❓";
      default: return "⏳";
    }
  };

  const getStatusText = (status: ParticipantStatusEnum) => {
    switch (status) {
      case "accepted": return "Accepté";
      case "declined": return "Refusé";
      case "maybe": return "Peut-être";
      default: return "En attente";
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">Événement introuvable.</Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const isOrganizer = session?.user?.id === event.organizer_id;

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: "#f9fafb" }}
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl">
              Participants - {event.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-muted-foreground">
              {participants.length} participant{participants.length > 1 ? "s" : ""}
            </Text>
          </CardContent>
        </Card>

        {/* Inviter un participant (organisateur seulement) */}
        {isOrganizer && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Inviter un participant</CardTitle>
            </CardHeader>
            <CardContent className="gap-4">
              <View>
                <Label className="mb-2">Email du participant</Label>
                <Input
                  placeholder="<EMAIL>"
                  value={inviteEmail}
                  onChangeText={setInviteEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
              <Button
                onPress={handleInviteParticipant}
                disabled={inviteLoading || !inviteEmail.trim()}
                className="w-full"
              >
                <Text className="text-white">
                  {inviteLoading ? "Envoi..." : "Envoyer l'invitation"}
                </Text>
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Liste des participants */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Liste des participants</CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            {participants.map((participant) => (
              <View
                key={participant.id}
                className="flex-row items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <View className="flex-1">
                  <Text className="font-medium">
                    {participant.anonymous_name || 
                     participant.anonymous_email || 
                     "Utilisateur"}
                  </Text>
                  <Text className="text-sm text-muted-foreground">
                    {participant.role === "organizer" ? "Organisateur" : "Invité"}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Text className="mr-2">{getStatusEmoji(participant.status)}</Text>
                  <Text className="text-sm">{getStatusText(participant.status)}</Text>
                </View>
              </View>
            ))}
            
            {participants.length === 0 && (
              <Text className="text-center text-muted-foreground py-8">
                Aucun participant pour le moment
              </Text>
            )}
          </CardContent>
        </Card>

        {/* Bouton retour */}
        <Button
          variant="outline"
          onPress={() => router.back()}
          className="mt-6 w-full"
        >
          <Text>Revenir aux détails de l'événement</Text>
        </Button>
      </View>
    </ScrollView>
  );
}
