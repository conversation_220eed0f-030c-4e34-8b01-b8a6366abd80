// --- Begin app/event/[id]/_layout.tsx ---
import { Stack } from "expo-router";
import React from "react";

export default function EventDetailLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="index"
        options={{
          title: "Détails de l'événement",
          presentation: "card",
        }}
      />
    </Stack>
  );
}

// --- End app/event/[id]/_layout.tsx ---
