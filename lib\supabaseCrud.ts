// --- Begin lib/supabaseCrud.ts ---
import { supabase, supabaseAdmin } from "./supabase";
import { Database } from "./database.types"; // Use generated types
import { PostgrestError } from "@supabase/supabase-js";

// Define aliases for generated types for convenience
type DbTables = Database["public"]["Tables"];
type DbEnums = Database["public"]["Enums"];

type Profile = DbTables["profiles"]["Row"];
type ProfileInsert = DbTables["profiles"]["Insert"];
type ProfileUpdate = DbTables["profiles"]["Update"];

type Event = DbTables["events"]["Row"];
type EventInsert = DbTables["events"]["Insert"];
type EventUpdate = DbTables["events"]["Update"];

type Participant = DbTables["participants"]["Row"];
export type ParticipantInsert = DbTables["participants"]["Insert"];
type ParticipantUpdate = DbTables["participants"]["Update"];

type Item = DbTables["items"]["Row"];
export type ItemInsert = DbTables["items"]["Insert"];
type ItemUpdate = DbTables["items"]["Update"];

type Contact = DbTables["contacts"]["Row"];
export type ContactInsert = DbTables["contacts"]["Insert"];
type ContactUpdate = DbTables["contacts"]["Update"];

type ContactGroup = DbTables["contact_groups"]["Row"];
type ContactGroupInsert = DbTables["contact_groups"]["Insert"];
type ContactGroupUpdate = DbTables["contact_groups"]["Update"];

type ContactGroupMember = DbTables["contact_group_members"]["Row"];
type ContactGroupMemberInsert = DbTables["contact_group_members"]["Insert"];

// Enums from generated types
type ParticipantRoleEnum = DbEnums["participant_role"];
type ParticipantStatusEnum = DbEnums["participant_status"];

// Type returned by the get_events_for_user RPC function
type EventFromRpc =
  Database["public"]["Functions"]["get_events_for_user"]["Returns"][number];
// Custom combined type for Event list display
export interface EventWithParticipantStatus extends EventFromRpc {
  is_organizer: boolean;
  participant_status?: ParticipantStatusEnum | null;
  participant_role?: ParticipantRoleEnum | null;
}

// Helper function to handle Supabase errors
function handleError(error: PostgrestError | null, context: string): void {
  if (error) {
    console.error(`Supabase error in ${context}:`, error.message);
    console.error(`Error details:`, error);

    // Log specific error types for better debugging
    if (error.message.includes("infinite recursion")) {
      console.warn(
        `RLS recursion detected in ${context} - consider using admin client`
      );
    }
    if (error.message.includes("does not exist")) {
      console.warn(`Missing database object in ${context} - check schema`);
    }
    if (error.message.includes("permission denied")) {
      console.warn(`Permission denied in ${context} - check RLS policies`);
    }
  }
}

// --- Profiles ---

export async function fetchProfile(userId: string): Promise<Profile | null> {
  try {
    // Try with admin client directly to avoid RLS issues
    const { data, error } = await supabaseAdmin
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (error) {
      console.warn("fetchProfile (admin): Error occurred:", error.message);
      // If there's still an error with admin client, create profile directly
      if (error.code === "PGRST116" || error.message.includes("0 rows")) {
        return await createProfile(userId, {
          name: "Utilisateur",
          avatar_url: null,
        });
      }
      handleError(error, "fetchProfile (admin)");
      return null;
    }

    // If no profile exists, create one automatically
    if (!data) {
      return await createProfile(userId, {
        name: "Utilisateur",
        avatar_url: null,
      });
    }

    return data;
  } catch (e) {
    console.error("Exception in fetchProfile:", e);
    // Last resort: create a profile manually
    try {
      return await createProfile(userId, {
        name: "Utilisateur",
        avatar_url: null,
      });
    } catch (createError) {
      console.error("Failed to create profile as last resort:", createError);
      return null;
    }
  }
}

export async function createProfile(
  userId: string,
  profileData: Omit<ProfileInsert, "id">
): Promise<Profile | null> {
  try {
    // Use admin client to bypass any RLS issues during profile creation
    const { data, error } = await supabaseAdmin
      .from("profiles")
      .insert({
        id: userId,
        ...profileData,
      })
      .select()
      .single();

    if (error) {
      handleError(error, "createProfile (admin)");
      return null;
    }

    return data;
  } catch (e) {
    console.error("Exception in createProfile:", e);
    return null;
  }
}

export async function updateProfile(
  userId: string,
  updates: ProfileUpdate
): Promise<Profile | null> {
  const { id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("profiles")
    .update(updatePayload)
    .eq("id", userId)
    .select()
    .single();
  handleError(error, "updateProfile");
  return data;
}

export async function searchProfiles(query: string): Promise<Profile[]> {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .ilike("name", `%${query}%`)
    .limit(10);
  handleError(error, "searchProfiles");
  return data || [];
}

// --- Events ---

export async function fetchEventsForUser(
  userId: string
): Promise<EventWithParticipantStatus[]> {
  try {
    // Try RPC function first
    const { data, error } = await supabase.rpc("get_events_for_user", {
      user_id_param: userId,
    });

    // If RPC function doesn't exist or fails, use fallback
    if (
      error?.message.includes("does not exist") ||
      error?.message.includes("function")
    ) {
      console.warn(
        "RPC function get_events_for_user not found, using fallback"
      );
      return await fetchEventsForUserFallback(userId);
    }

    if (error) {
      handleError(error, "fetchEventsForUser (RPC)");
      return await fetchEventsForUserFallback(userId);
    }

    if (!data) return [];

    // Map the RPC result, assuming it returns fields compatible with EventFromRpc
    return data.map((event) => ({
      ...event,
      is_organizer: event.organizer_id === userId,
      participant_status: null, // Placeholder - RPC doesn't return this
      participant_role: null, // Placeholder - RPC doesn't return this
    }));
  } catch (e) {
    console.error("Exception in fetchEventsForUser:", e);
    return await fetchEventsForUserFallback(userId);
  }
}

// Fallback function using direct queries
async function fetchEventsForUserFallback(
  userId: string
): Promise<EventWithParticipantStatus[]> {
  try {
    // Get events where user is organizer
    const { data: organizedEvents, error: organizedError } = await supabaseAdmin
      .from("events")
      .select("*")
      .eq("organizer_id", userId);

    if (organizedError) {
      handleError(
        organizedError,
        "fetchEventsForUserFallback - organized events"
      );
    }

    // Get events where user is participant
    const { data: participantEvents, error: participantError } =
      await supabaseAdmin
        .from("participants")
        .select("event_id, events(*)")
        .eq("user_id", userId);

    if (participantError) {
      handleError(
        participantError,
        "fetchEventsForUserFallback - participant events"
      );
    }

    // Combine and deduplicate events
    const allEvents: Event[] = [];
    const eventIds = new Set<number>();

    // Add organized events
    if (organizedEvents) {
      organizedEvents.forEach((event) => {
        if (!eventIds.has(event.id)) {
          allEvents.push(event);
          eventIds.add(event.id);
        }
      });
    }

    // Add participant events
    if (participantEvents) {
      participantEvents.forEach((p) => {
        if (p.events && !eventIds.has(p.events.id)) {
          allEvents.push(p.events);
          eventIds.add(p.events.id);
        }
      });
    }

    // Map to EventWithParticipantStatus
    return allEvents.map((event) => ({
      ...event,
      is_organizer: event.organizer_id === userId,
      participant_status: null,
      participant_role: null,
    }));
  } catch (e) {
    console.error("Exception in fetchEventsForUserFallback:", e);
    return [];
  }
}

// Helper for admin fetch (used as fallback) - returns base Event type
async function fetchEventDetailsWithAdmin(
  eventId: number
): Promise<Event | null> {
  const { data, error } = await supabaseAdmin
    .from("events")
    .select("*")
    .eq("id", eventId)
    .single();
  handleError(error, "fetchEventDetails (admin client)");
  return data;
}

export async function fetchEventDetails(
  eventId: number
): Promise<Event | null> {
  try {
    // Try with standard client first
    const { data, error } = await supabase
      .from("events")
      .select("*")
      .eq("id", eventId)
      .single();

    // If any error occurs, immediately fallback to admin client
    if (error) {
      console.warn(
        "fetchEventDetails: Standard client failed, trying admin client:",
        error.message
      );
      return fetchEventDetailsWithAdmin(eventId);
    }

    return data;
  } catch (e) {
    console.error("Exception in fetchEventDetails:", e);
    // Fallback to admin client on any exception
    console.warn("fetchEventDetails: Exception occurred, trying admin client");
    return fetchEventDetailsWithAdmin(eventId);
  }
}

export async function createEvent(
  eventData: Omit<
    EventInsert,
    "id" | "created_at" | "updated_at" | "organizer_id"
  >,
  organizerId: string
): Promise<Event | null> {
  if (!eventData.title || !eventData.date_time) {
    return null;
  }
  const eventDate = new Date(eventData.date_time);
  if (eventDate <= new Date()) {
    throw new Error("La date de l'événement doit être dans le futur");
  }
  try {
    const eventPayload: EventInsert = {
      ...eventData,
      organizer_id: organizerId,
    };

    // Try with standard client first
    const { data, error } = await supabase
      .from("events")
      .insert(eventPayload)
      .select()
      .single();

    // If RLS recursion error, fallback to admin client
    if (error && error.message.includes("infinite recursion")) {
      console.warn("createEvent: RLS recursion detected, trying admin client");
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from("events")
        .insert(eventPayload)
        .select()
        .single();

      if (adminError) {
        handleError(adminError, "createEvent (admin fallback)");
        return null;
      }
      return adminData;
    }

    if (error) {
      handleError(error, "createEvent");
      return null;
    }
    return data;
  } catch (e) {
    console.error("Exception in createEvent:", e);
    if (e instanceof Error) throw e;
    else throw new Error("Erreur lors de la création de l'événement");
  }
}

export async function updateEvent(
  eventId: number,
  updates: EventUpdate
): Promise<Event | null> {
  const { id, organizer_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  try {
    const { data, error } = await supabase
      .from("events")
      .update(updatePayload)
      .eq("id", eventId)
      .select()
      .single();
    if (error) {
      handleError(error, "updateEvent");
      return null;
    }
    return data;
  } catch (e) {
    console.error("Exception in updateEvent:", e);
    return null;
  }
}

export async function deleteEvent(eventId: number): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from("events")
      .delete()
      .eq("id", eventId);
    if (error) {
      handleError(error, "deleteEvent (admin client)");
      return false;
    }
    return true;
  } catch (e) {
    console.error("Exception in deleteEvent:", e);
    return false;
  }
}

// --- Participants ---

// Let function return type be inferred from query + generated types
export async function fetchParticipantsForEvent(eventId: number) {
  try {
    // Try with standard client first
    const { data, error } = await supabase
      .from("participants")
      .select(`*, profiles ( name, avatar_url )`)
      .eq("event_id", eventId)
      .order("role", { ascending: true })
      .order("created_at", { ascending: true });

    // If any error occurs, immediately fallback to admin client
    if (error) {
      console.warn(
        "fetchParticipantsForEvent: Standard client failed, trying admin client:",
        error.message
      );
      // Fallback to admin client
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from("participants")
        .select(`*, profiles ( name, avatar_url )`)
        .eq("event_id", eventId)
        .order("role", { ascending: true })
        .order("created_at", { ascending: true });

      if (adminError) {
        handleError(adminError, "fetchParticipantsForEvent (admin fallback)");
        return [];
      }

      return (adminData || []).map((p) => ({
        ...p,
        name_display: p.user_id ? p.profiles?.name ?? null : p.anonymous_name,
      }));
    }

    // Add name_display helper after fetching
    return (data || []).map((p) => ({
      ...p,
      // Check if profiles is not null before accessing name
      name_display: p.user_id ? p.profiles?.name ?? null : p.anonymous_name,
    }));
  } catch (e) {
    console.error("Exception in fetchParticipantsForEvent:", e);
    // Fallback to admin client on any exception
    console.warn(
      "fetchParticipantsForEvent: Exception occurred, trying admin client"
    );
    try {
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from("participants")
        .select(`*, profiles ( name, avatar_url )`)
        .eq("event_id", eventId)
        .order("role", { ascending: true })
        .order("created_at", { ascending: true });

      if (adminError) {
        handleError(
          adminError,
          "fetchParticipantsForEvent (admin exception fallback)"
        );
        return [];
      }

      return (adminData || []).map((p) => ({
        ...p,
        name_display: p.user_id ? p.profiles?.name ?? null : p.anonymous_name,
      }));
    } catch (adminE) {
      console.error("Admin fallback also failed:", adminE);
      return [];
    }
  }
}

export async function createParticipant(
  participantData: ParticipantInsert
): Promise<Participant | null> {
  // Return generated Participant Row type
  if (
    !participantData.event_id ||
    (!participantData.user_id && !participantData.anonymous_name)
  ) {
    console.error("Missing required fields for participant");
    return null;
  }
  const insertData: ParticipantInsert = { ...participantData };
  if (insertData.user_id) {
    insertData.anonymous_name = null;
    insertData.anonymous_email = null;
    insertData.anonymous_phone = null;
  }
  try {
    // Try with standard client first
    const { data, error } = await supabase
      .from("participants")
      .insert(insertData)
      .select()
      .single();

    // If RLS recursion error, fallback to admin client
    if (error && error.message.includes("infinite recursion")) {
      console.warn(
        "createParticipant: RLS recursion detected, trying admin client"
      );
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from("participants")
        .insert(insertData)
        .select()
        .single();

      if (adminError) {
        handleError(adminError, "createParticipant (admin fallback)");
        return null;
      }
      return adminData;
    }

    if (error) {
      handleError(error, `createParticipant`);
      return null;
    }
    return data;
  } catch (e) {
    console.error("Exception in createParticipant:", e);
    return null;
  }
}

export async function updateParticipant(
  participantId: number,
  updates: ParticipantUpdate
): Promise<Participant | null> {
  // Return generated Participant Row type
  const { id, event_id, user_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("participants")
    .update(updatePayload)
    .eq("id", participantId)
    .select()
    .single();
  handleError(error, "updateParticipant");
  return data;
}

export async function updateParticipantStatus(
  participantId: number,
  status: ParticipantStatusEnum
): Promise<Participant | null> {
  return updateParticipant(participantId, { status });
}

export async function deleteParticipant(
  participantId: number
): Promise<boolean> {
  const { error } = await supabase
    .from("participants")
    .delete()
    .eq("id", participantId);
  handleError(error, "deleteParticipant");
  return !error;
}

export async function fetchMyParticipantRecord(
  eventId: number,
  userId: string
): Promise<Participant | null> {
  // Return generated Participant Row type
  // Use admin client to avoid RLS issues
  const { data, error } = await supabaseAdmin
    .from("participants")
    .select("*")
    .eq("event_id", eventId)
    .eq("user_id", userId)
    .maybeSingle();
  handleError(error, "fetchMyParticipantRecord (admin)");
  return data;
}

// --- Items ---

// Let function return type be inferred from query + generated types
export async function fetchItemsForEvent(eventId: number) {
  try {
    // Use admin client to bypass RLS recursion error
    const { data, error } = await supabaseAdmin
      .from("items")
      .select(
        `
        *,
        suggester:profiles!suggester_id ( name, avatar_url ),
        assigned_participant:participants!assigned_participant_id (
            id, user_id, anonymous_name,
            profiles ( name, avatar_url )
        ),
        fixed_by_participant:participants!fixed_by_participant_id (
            id, user_id, anonymous_name,
            profiles ( name, avatar_url )
        )
      `
      )
      .eq("event_id", eventId)
      .order("category")
      .order("created_at");

    handleError(error, "fetchItemsForEvent");
    if (!data) return [];

    // Add name_display helper to nested participants safely
    return data.map((item: any) => ({
      ...item,
      assigned_participant: item.assigned_participant
        ? {
            ...item.assigned_participant,
            name_display: item.assigned_participant.user_id
              ? item.assigned_participant.profiles?.name ?? null
              : item.assigned_participant.anonymous_name,
          }
        : null,
      fixed_by_participant: item.fixed_by_participant
        ? {
            ...item.fixed_by_participant,
            name_display: item.fixed_by_participant.user_id
              ? item.fixed_by_participant.profiles?.name ?? null
              : item.fixed_by_participant.anonymous_name,
          }
        : null,
    }));
  } catch (e) {
    console.error("Exception in fetchItemsForEvent:", e);
    return [];
  }
}

// Let function return type be inferred
export async function fetchItemsAssignedToUser(userId: string) {
  try {
    // Simplified approach: directly query items table with admin client
    // This avoids the complex participant lookup that causes RLS issues
    const { data: items, error: itemsError } = await supabaseAdmin
      .from("items")
      .select(
        `
        *,
        events!event_id (
          id, title, icon, date_time
        )
      `
      )
      .not("assigned_participant_id", "is", null); // Only get items that are assigned

    if (itemsError) {
      handleError(
        itemsError,
        "fetchItemsAssignedToUser - direct items (admin)"
      );

      // Fallback: return empty array to avoid breaking the app
      return [];
    }

    if (!items || items.length === 0) {
      return [];
    }

    // Filter items by checking if the assigned participant belongs to the user
    // This is a workaround to avoid the RLS recursion issue
    const userItems = [];

    for (const item of items) {
      if (item.assigned_participant_id) {
        try {
          const { data: participant } = await supabaseAdmin
            .from("participants")
            .select("user_id")
            .eq("id", item.assigned_participant_id)
            .single();

          if (participant && participant.user_id === userId) {
            userItems.push(item);
          }
        } catch (participantError) {
          // Skip this item if we can't verify the participant
        }
      }
    }

    return userItems;
  } catch (e) {
    console.error("Exception in fetchItemsAssignedToUser:", e);
    return [];
  }
}

export async function createItem(itemData: ItemInsert): Promise<Item | null> {
  // Return generated Item Row type
  // Use admin client to avoid RLS recursion issues
  const { data, error } = await supabaseAdmin
    .from("items")
    .insert(itemData)
    .select()
    .single();
  handleError(error, "createItem (admin)");
  return data;
}

export async function updateItem(
  itemId: number,
  updates: ItemUpdate
): Promise<Item | null> {
  // Return generated Item Row type
  const { id, event_id, suggester_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  // Use admin client to avoid RLS recursion issues
  const { data, error } = await supabaseAdmin
    .from("items")
    .update(updatePayload)
    .eq("id", itemId)
    .select()
    .single();
  handleError(error, "updateItem (admin)");
  return data;
}

export async function deleteItem(itemId: number): Promise<boolean> {
  // Use admin client to avoid RLS recursion issues
  const { error } = await supabaseAdmin.from("items").delete().eq("id", itemId);
  handleError(error, "deleteItem (admin)");
  return !error;
}

export async function assignItemToParticipant(
  itemId: number,
  participantId: number | null
): Promise<Item | null> {
  return updateItem(itemId, { assigned_participant_id: participantId });
}

export async function fixItemForParticipant(
  itemId: number,
  participantId: number
): Promise<Item | null> {
  return updateItem(itemId, { fixed_by_participant_id: participantId });
}

export async function unfixItem(itemId: number): Promise<Item | null> {
  return updateItem(itemId, { fixed_by_participant_id: null });
}

// --- Contacts ---

// Let function return type be inferred
export async function fetchContacts(userId: string) {
  const { data, error } = await supabase
    .from("contacts")
    .select(`*, contact_profile:contact_profile_id ( name, avatar_url )`)
    .eq("user_id", userId)
    .order("name");
  handleError(error, "fetchContacts");
  return data || [];
}

export async function createContact(
  contactData: ContactInsert
): Promise<Contact | null> {
  // Return generated Contact Row type
  const { data, error } = await supabase
    .from("contacts")
    .insert(contactData)
    .select()
    .single();
  handleError(error, "createContact");
  return data;
}

export async function updateContact(
  contactId: number,
  updates: ContactUpdate
): Promise<Contact | null> {
  // Return generated Contact Row type
  const { id, user_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("contacts")
    .update(updatePayload)
    .eq("id", contactId)
    .select()
    .single();
  handleError(error, "updateContact");
  return data;
}

export async function deleteContact(contactId: number): Promise<boolean> {
  const { error } = await supabase
    .from("contacts")
    .delete()
    .eq("id", contactId);
  handleError(error, "deleteContact");
  return !error;
}

// --- Contact Groups ---

export async function fetchContactGroups(
  userId: string
): Promise<ContactGroup[]> {
  // Return generated ContactGroup Row type
  const { data, error } = await supabase
    .from("contact_groups")
    .select("*")
    .eq("user_id", userId)
    .order("name");
  handleError(error, "fetchContactGroups");
  return data || [];
}

export async function createContactGroup(
  groupData: ContactGroupInsert
): Promise<ContactGroup | null> {
  // Return generated ContactGroup Row type
  const { data, error } = await supabase
    .from("contact_groups")
    .insert(groupData)
    .select()
    .single();
  handleError(error, "createContactGroup");
  return data;
}

export async function updateContactGroup(
  groupId: number,
  updates: ContactGroupUpdate
): Promise<ContactGroup | null> {
  // Return generated ContactGroup Row type
  const { id, user_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("contact_groups")
    .update(updatePayload)
    .eq("id", groupId)
    .select()
    .single();
  handleError(error, "updateContactGroup");
  return data;
}

export async function deleteContactGroup(groupId: number): Promise<boolean> {
  const { error } = await supabase
    .from("contact_groups")
    .delete()
    .eq("id", groupId);
  handleError(error, "deleteContactGroup");
  return !error;
}

// --- Contact Group Members ---

export async function addContactToGroup(
  groupId: number,
  contactId: number
): Promise<boolean> {
  const payload: ContactGroupMemberInsert = {
    group_id: groupId,
    contact_id: contactId,
  };
  const { error } = await supabase
    .from("contact_group_members")
    .insert(payload);
  handleError(error, "addContactToGroup");
  return !error;
}

export async function removeContactFromGroup(
  groupId: number,
  contactId: number
): Promise<boolean> {
  const { error } = await supabase
    .from("contact_group_members")
    .delete()
    .match({ group_id: groupId, contact_id: contactId });
  handleError(error, "removeContactFromGroup");
  return !error;
}

// Let function return type be inferred
export async function fetchContactsInGroup(groupId: number) {
  const { data, error } = await supabase
    .from("contact_group_members")
    .select(
      `
      contact_id, group_id,
      contacts!inner(
        *,
        contact_profile:contact_profile_id ( name, avatar_url )
      )
      `
    )
    .eq("group_id", groupId);

  handleError(error, "fetchContactsInGroup");
  // Map to extract the nested contact data, ensuring type safety
  // The inferred type of `data` will be complex, adjust component usage accordingly
  return data?.map((item) => item.contacts).filter(Boolean) || [];
}

// --- Messages --- (Commented out)
/* ... */
// Fonction pour récupérer un événement par token d'invitation
export async function fetchEventByInvitationToken(token: string) {
  try {
    const { data, error } = await supabase
      .from("participants")
      .select(
        `
        event_id,
        events (*)
      `
      )
      .eq("invitation_token", token)
      .single();

    if (error) {
      console.error("Error fetching event by invitation token:", error);
      return null;
    }

    return data?.events as Event | null;
  } catch (error) {
    console.error("Error in fetchEventByInvitationToken:", error);
    return null;
  }
}

// Fonction pour récupérer un participant par token d'invitation
export async function fetchParticipantByToken(token: string) {
  try {
    const { data, error } = await supabase
      .from("participants")
      .select("*")
      .eq("invitation_token", token)
      .single();

    if (error) {
      console.error("Error fetching participant by token:", error);
      return null;
    }

    return data as Participant | null;
  } catch (error) {
    console.error("Error in fetchParticipantByToken:", error);
    return null;
  }
}

// --- End lib/supabaseCrud.ts ---
