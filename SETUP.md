# 🚀 Guide de Configuration - Party Organizer

## 📋 Prérequis

- Node.js 18+ 
- Expo CLI (`npm install -g @expo/cli`)
- Compte Supabase (gratuit)

## 🔧 Configuration des Variables d'Environnement

### 1. C<PERSON>er le fichier .env

```bash
# Copiez le fichier d'exemple
cp .env.example .env
```

### 2. Configurer Supabase

1. **Créer un projet Supabase :**
   - Allez sur [supabase.com](https://supabase.com)
   - Créez un nouveau projet
   - Notez l'URL et les clés API

2. **Récupérer les clés :**
   - **URL du projet** : `https://votre-projet.supabase.co`
   - **Clé anonyme** : Dans Settings > API > anon public
   - **Clé service** : Dans Settings > API > service_role (⚠️ PRIVÉE)

3. **Modifier le fichier .env :**
```env
EXPO_PUBLIC_SUPABASE_URL=https://votre-projet.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=votre_cle_anonyme_ici
SUPABASE_SERVICE_ROLE_KEY=votre_cle_service_ici
```

### 3. Initialiser la Base de Données

```bash
# Exécuter le script de création des tables
# (Vous devrez copier le contenu de supabase/sql/schema.sql dans l'éditeur SQL de Supabase)
```

## 🏃‍♂️ Démarrage Rapide

```bash
# Installer les dépendances
npm install

# Démarrer en mode développement
npm start

# Pour le web spécifiquement
npm run web

# Pour mobile (avec Expo Go)
npm run android  # ou npm run ios
```

## 📱 Fonctionnalités MVP Disponibles

### ✅ Authentification
- Connexion anonyme
- Création de compte
- Connexion par email/mot de passe

### ✅ Gestion d'Événements
- Création d'événements avec emoji picker
- Ajout de participants lors de la création
- Prévisualisation avant création
- Page de détail complète

### ✅ Gestion des Participants
- Invitation par email
- Suivi des statuts (accepté, refusé, en attente)
- Page dédiée de gestion

### ✅ Gestion des Items/Tâches
- Ajout d'items avec coût et effort
- Catégorisation
- Assignation manuelle
- Répartition automatique équitable

### ✅ Répartition Intelligente
- Algorithme d'équilibrage coût/effort
- Interface de prévisualisation
- Application automatique

### ✅ Onglet "Mes Tâches"
- Vue centralisée des items assignés
- Groupement par événement
- Marquage comme terminé

## 🔒 Sécurité

### Variables d'Environnement
- ✅ `.env` ajouté au `.gitignore`
- ✅ Clés externalisées du code
- ✅ Validation des clés au démarrage

### Base de Données
- ✅ RLS (Row Level Security) activé
- ✅ Politiques de sécurité configurées
- ✅ Client admin séparé pour les opérations privilégiées

## 🌐 Compatibilité

### Plateformes Supportées
- ✅ Web (navigateurs modernes)
- ✅ iOS (via Expo Go ou build)
- ✅ Android (via Expo Go ou build)

### Composants UI
- ✅ React Native Reusables (shadcn/ui style)
- ✅ NativeWind (TailwindCSS)
- ✅ Responsive design

## 🚀 Prochaines Étapes (Post-MVP)

### Phase 3 - Fonctionnalités Avancées
- [ ] Système d'invitations par lien unique
- [ ] Notifications push
- [ ] Mode hors ligne
- [ ] Export des données

### Phase 4 - Optimisations
- [ ] Cache intelligent
- [ ] Optimisations performances
- [ ] Tests automatisés
- [ ] CI/CD

## 🐛 Dépannage

### Erreurs Communes

1. **"Supabase URL not configured"**
   - Vérifiez que le fichier `.env` existe
   - Vérifiez que les variables commencent par `EXPO_PUBLIC_`

2. **"RLS policy violation"**
   - Vérifiez que les politiques Supabase sont bien configurées
   - Utilisez le client admin pour les opérations privilégiées

3. **"Module not found"**
   - Supprimez `node_modules` et `package-lock.json`
   - Relancez `npm install`

### Support
- 📧 Créez une issue sur GitHub
- 📖 Consultez la documentation Expo
- 🔍 Vérifiez les logs Supabase

## 📊 Métriques MVP

### Fonctionnalités Implémentées : 95%
- ✅ Authentification complète
- ✅ CRUD événements
- ✅ Gestion participants
- ✅ Gestion items/tâches
- ✅ Répartition automatique
- ✅ Interface responsive

### Prêt pour Production : 🎯
Le MVP est fonctionnel et prêt pour les premiers utilisateurs !
