import React, { useState } from 'react';
import { View, Modal, Pressable, Platform, Dimensions, ScrollView } from 'react-native';
import { Text } from '~/components/ui/text';
import { CustomButton as Button } from '~/components/ui/custom-button';
import { cn } from '~/lib/utils';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';

export interface SelectOption {
  label: string;
  value: string;
}

interface ModernSelectProps {
  options: SelectOption[];
  value?: SelectOption | null;
  onValueChange: (option: SelectOption | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  label?: string;
  error?: string;
}

export function ModernSelect({
  options,
  value,
  onValueChange,
  placeholder = "Sélectionner une option",
  className,
  disabled = false,
  label,
  error,
}: ModernSelectProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const { width: screenWidth } = Dimensions.get('window');
  const isDesktop = Platform.OS === 'web' && screenWidth > 768;

  const handleSelect = (option: SelectOption) => {
    onValueChange(option);
    setModalVisible(false);
  };

  const handleClear = () => {
    onValueChange(null);
    setModalVisible(false);
  };

  const renderDesktopDropdown = () => {
    if (!modalVisible) return null;

    return (
      <Animated.View
        entering={FadeIn.duration(150)}
        exiting={FadeOut.duration(100)}
        className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60"
      >
        <ScrollView className="py-1">
          {value && (
            <Pressable
              onPress={handleClear}
              className="px-4 py-3 hover:bg-gray-50 active:bg-gray-100 border-b border-gray-100"
            >
              <Text className="text-red-600 font-medium">Effacer la sélection</Text>
            </Pressable>
          )}
          {options.map((option) => (
            <Pressable
              key={option.value}
              onPress={() => handleSelect(option)}
              className={cn(
                "px-4 py-3 hover:bg-blue-50 active:bg-blue-100 flex-row justify-between items-center",
                value?.value === option.value && "bg-blue-50"
              )}
            >
              <Text className={cn(
                "text-base",
                value?.value === option.value ? "text-blue-600 font-medium" : "text-gray-900"
              )}>
                {option.label}
              </Text>
              {value?.value === option.value && (
                <Text className="text-blue-600 font-medium">✓</Text>
              )}
            </Pressable>
          ))}
        </ScrollView>
      </Animated.View>
    );
  };

  const renderMobileModal = () => (
    <Modal
      visible={modalVisible}
      transparent={true}
      animationType="none"
      onRequestClose={() => setModalVisible(false)}
    >
      <Pressable 
        className="flex-1 bg-black/50 justify-end"
        onPress={() => setModalVisible(false)}
      >
        <Animated.View
          entering={SlideInDown.duration(300)}
          exiting={SlideOutDown.duration(200)}
          className="bg-white rounded-t-2xl max-h-[70%] shadow-2xl"
        >
          <Pressable onPress={(e) => e.stopPropagation()}>
            {/* Header */}
            <View className="flex-row justify-between items-center p-6 border-b border-gray-100">
              <Text className="text-lg font-semibold text-gray-900">{label || "Sélectionner"}</Text>
              <Button variant="ghost" size="sm" onPress={() => setModalVisible(false)}>
                <Text className="text-xl text-gray-400">✕</Text>
              </Button>
            </View>

            {/* Options */}
            <ScrollView className="max-h-80">
              {value && (
                <Pressable
                  onPress={handleClear}
                  className="p-4 border-b border-gray-50 active:bg-red-50"
                >
                  <Text className="text-red-600 font-medium text-center">Effacer la sélection</Text>
                </Pressable>
              )}

              {options.map((option) => (
                <Pressable
                  key={option.value}
                  onPress={() => handleSelect(option)}
                  className={cn(
                    "p-4 border-b border-gray-50 active:bg-blue-50 flex-row justify-between items-center",
                    value?.value === option.value && "bg-blue-50"
                  )}
                >
                  <Text className={cn(
                    "text-base",
                    value?.value === option.value ? "text-blue-600 font-semibold" : "text-gray-900"
                  )}>
                    {option.label}
                  </Text>
                  {value?.value === option.value && (
                    <Text className="text-blue-600 text-lg">✓</Text>
                  )}
                </Pressable>
              ))}
            </ScrollView>
          </Pressable>
        </Animated.View>
      </Pressable>
    </Modal>
  );

  return (
    <View className="relative">
      {/* Trigger Button */}
      <Pressable
        onPress={() => !disabled && setModalVisible(true)}
        className={cn(
          "flex flex-row h-12 items-center justify-between rounded-xl border-2 bg-white px-4 py-3 shadow-sm transition-all",
          "hover:shadow-md focus:shadow-md",
          error ? "border-red-300 focus:border-red-500 focus:ring-red-500/20" : 
          value ? "border-blue-300 focus:border-blue-500 focus:ring-blue-500/20" : 
          "border-gray-200 hover:border-gray-300 focus:border-blue-500 focus:ring-blue-500/20",
          disabled && "opacity-50 cursor-not-allowed bg-gray-50",
          className
        )}
        disabled={disabled}
        style={({ pressed }) => ({
          transform: [{ scale: pressed ? 0.98 : 1 }],
        })}
      >
        <Text className={cn(
          "text-base flex-1",
          value ? "text-gray-900 font-medium" : "text-gray-500"
        )}>
          {value ? value.label : placeholder}
        </Text>
        <View className="ml-3">
          <Animated.View
            style={{
              transform: [{ rotate: modalVisible ? '180deg' : '0deg' }],
            }}
          >
            <Text className="text-gray-400 text-sm">▼</Text>
          </Animated.View>
        </View>
      </Pressable>

      {/* Error Message */}
      {error && (
        <Text className="text-red-600 text-sm mt-1 ml-1">{error}</Text>
      )}

      {/* Desktop Dropdown or Mobile Modal */}
      {isDesktop ? renderDesktopDropdown() : renderMobileModal()}
    </View>
  );
}
