import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  ActivityIndicator,
  FlatList,
  Pressable,
  Alert,
} from "react-native";
import { Text } from "~/components/ui/text";
import { CustomButton as Button } from "~/components/ui/custom-button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";
// Use generated types
import { Database } from "~/lib/database.types";
import {
  fetchItemsForEvent, // This function now returns the inferred type based on its query
  deleteItem,
  fetchMyParticipantRecord,
  fixItemForParticipant,
  unfixItem,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
// Icônes remplacées par des emojis pour compatibilité web
import { AddItemForm } from "./AddItemForm";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
  DialogDescription,
} from "~/components/ui/dialog";

// Define aliases for generated types used in this component
// Infer the complex type returned by fetchItemsForEvent, including potential nulls for relations
type ItemFromFetch = Awaited<ReturnType<typeof fetchItemsForEvent>>[number];
type Participant = Database["public"]["Tables"]["participants"]["Row"];
type CostEnumUnion = Database["public"]["Enums"]["cost_enum"];
type EffortEnumUnion = Database["public"]["Enums"]["effort_enum"];
// Base Item Row type for editing state
type BaseItem = Database["public"]["Tables"]["items"]["Row"];

interface EventItemsSectionProps {
  eventId: number;
  isOrganizer: boolean;
  allowPreAssignment: boolean;
}

// Update helpers to accept string literal unions
const costToString = (cost: CostEnumUnion | null) => {
  if (!cost) return "";
  return cost;
};

const effortToString = (effort: EffortEnumUnion | null) => {
  if (!effort) return "";
  switch (effort) {
    case "1":
      return "Faible";
    case "2":
      return "Moyen";
    case "3":
      return "Élevé";
    default:
      return "";
  }
};

export function EventItemsSection({
  eventId,
  isOrganizer,
  allowPreAssignment,
}: EventItemsSectionProps) {
  const { session } = useAuth();
  const [items, setItems] = useState<ItemFromFetch[]>([]); // Use inferred type
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showItemFormDialog, setShowItemFormDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<BaseItem | null>(null); // Use BaseItem type
  const [myParticipantRecord, setMyParticipantRecord] =
    useState<Participant | null>(null);
  const [participantLoading, setParticipantLoading] = useState(true);

  const loadData = useCallback(async () => {
    console.log(`[EventItemsSection ${eventId}] loadData called`);
    setLoading(true);
    setParticipantLoading(true);
    setError(null);
    try {
      const fetchedItems = await fetchItemsForEvent(eventId);
      const fetchedParticipant = session?.user?.id
        ? await fetchMyParticipantRecord(eventId, session.user.id)
        : null;

      console.log(
        `[EventItemsSection ${eventId}] Fetched ${fetchedItems.length} items.`
      );
      setItems(fetchedItems);
      setMyParticipantRecord(fetchedParticipant);
    } catch (e: any) {
      console.error("Error loading data:", e);
      setError("Impossible de charger les données de l'événement.");
      setItems([]);
      setMyParticipantRecord(null);
    } finally {
      setLoading(false);
      setParticipantLoading(false);
    }
  }, [eventId, session?.user?.id]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  useEffect(() => {
    console.log(
      `[EventItemsSection ${eventId}] Items state updated:`,
      items.map((i) => i.name)
    );
  }, [items, eventId]);

  const handleFixItem = async (item: ItemFromFetch) => {
    if (!myParticipantRecord) {
      showToast("Impossible de trouver votre enregistrement participant.", {
        type: "error",
      });
      return;
    }
    try {
      const updatedItem = await fixItemForParticipant(
        item.id,
        myParticipantRecord.id
      );
      if (updatedItem) {
        showToast(`Vous avez fixé l'item "${item.name}" !`, {
          type: "success",
        });
        loadData();
      } else {
        throw new Error("Échec de la fixation de l'item");
      }
    } catch (err) {
      showToast("Erreur lors de la fixation de l'item.", { type: "error" });
      console.error("Error fixing item:", err);
    }
  };

  const handleUnfixItem = async (item: ItemFromFetch) => {
    try {
      const updatedItem = await unfixItem(item.id);
      if (updatedItem) {
        showToast(`Vous avez libéré l'item "${item.name}".`, {
          type: "success",
        });
        loadData();
      } else {
        throw new Error("Échec de l'annulation de la fixation");
      }
    } catch (err) {
      showToast("Erreur lors de l'annulation.", { type: "error" });
      console.error("Error unfixing item:", err);
    }
  };

  const handleDeleteItem = async (itemId: number, itemName: string) => {
    Alert.alert(
      "Supprimer l'item",
      `Êtes-vous sûr de vouloir supprimer l'item "${itemName}" ? Cette action est irréversible.`,
      [
        { text: "Annuler", style: "cancel" },
        {
          text: "Supprimer",
          style: "destructive",
          onPress: async () => {
            try {
              const success = await deleteItem(itemId);
              if (success) {
                showToast("Item supprimé avec succès !", { type: "success" });
                loadData();
              } else {
                throw new Error("Échec de la suppression de l'item");
              }
            } catch (err) {
              showToast("Erreur lors de la suppression de l'item.", {
                type: "error",
              });
              console.error("Error deleting item:", err);
            }
          },
        },
      ]
    );
  };

  const handleOpenEditModal = (item: ItemFromFetch) => {
    // Extract base item properties for the form
    const baseItem: BaseItem = {
      id: item.id,
      event_id: item.event_id,
      suggester_id: item.suggester_id,
      name: item.name,
      category: item.category,
      estimated_cost: item.estimated_cost,
      estimated_effort: item.estimated_effort,
      is_suggestion: item.is_suggestion,
      is_personal: item.is_personal,
      assigned_participant_id: item.assigned_participant_id,
      fixed_by_participant_id: item.fixed_by_participant_id,
      created_at: item.created_at,
      updated_at: item.updated_at,
    };
    setEditingItem(baseItem);
    setShowItemFormDialog(true);
  };

  const handleOpenAddModal = () => {
    setEditingItem(null);
    setShowItemFormDialog(true);
  };

  const handleCloseModal = () => {
    setShowItemFormDialog(false);
    setEditingItem(null);
  };

  // Controlled open state for Dialog requires onOpenChange
  const handleOpenChange = (open: boolean) => {
    setShowItemFormDialog(open);
    if (!open) {
      setEditingItem(null); // Clear editing item on close
    }
  };

  if (loading || participantLoading) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <ActivityIndicator size="large" />
        <Text className="mt-2">Chargement...</Text>
      </View>
    );
  }
  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-destructive mb-2 text-5xl">⚠️</Text>
        <Text className="text-lg text-destructive text-center">{error}</Text>
        <Button onPress={loadData} className="mt-4">
          <Text>Réessayer</Text>
        </Button>
      </View>
    );
  }

  return (
    <Card className="mb-4 border border-border bg-white rounded-xl shadow-sm">
      <CardHeader className="flex-row justify-between items-center">
        <CardTitle className="text-lg font-medium">Items à Apporter</CardTitle>
        {isOrganizer && (
          <Dialog
            open={showItemFormDialog && !editingItem}
            onOpenChange={handleOpenChange}
          >
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" onPress={handleOpenAddModal}>
                <Text className="mr-2 text-lg">➕</Text>
                <Text>Ajouter</Text>
              </Button>
            </DialogTrigger>
            <DialogContent className="p-0 max-w-md">
              <DialogTitle className="sr-only">Ajouter un Item</DialogTitle>
              <DialogDescription className="sr-only">
                Formulaire pour ajouter un nouvel item à l'événement
              </DialogDescription>
              <AddItemForm
                eventId={eventId}
                itemToEdit={null} // Explicitly pass null for add mode
                onClose={handleCloseModal}
                onItemSaved={() => {
                  loadData();
                  handleCloseModal();
                }}
              />
            </DialogContent>
          </Dialog>
        )}
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <Text className="text-muted-foreground text-center py-4">
            {isOrganizer
              ? "Aucun item ajouté pour le moment. Commencez par en ajouter un !"
              : "Aucun item n'a encore été ajouté à la liste."}
          </Text>
        ) : (
          <FlatList
            data={items}
            extraData={items}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => {
              const canFix =
                allowPreAssignment &&
                !isOrganizer &&
                !item.assigned_participant_id &&
                !item.fixed_by_participant_id &&
                myParticipantRecord;
              const isFixedByMe =
                myParticipantRecord &&
                item.fixed_by_participant_id === myParticipantRecord.id;
              const fixedBySomeoneElse =
                item.fixed_by_participant_id && !isFixedByMe;

              return (
                <View className="py-3 border-b border-border/50 last:border-b-0">
                  <View className="flex-row justify-between items-start">
                    <View className="flex-1 mr-2">
                      <Text className="text-base font-semibold text-foreground">
                        {item.name}
                      </Text>
                      {item.category && (
                        <Text className="text-xs text-muted-foreground uppercase tracking-wider">
                          {item.category}
                        </Text>
                      )}
                      <View className="flex-row gap-4 mt-1">
                        {item.estimated_cost && (
                          <Text className="text-sm text-muted-foreground">
                            Coût: {costToString(item.estimated_cost)}
                          </Text>
                        )}
                        {item.estimated_effort && (
                          <Text className="text-sm text-muted-foreground">
                            Effort: {effortToString(item.estimated_effort)}
                          </Text>
                        )}
                      </View>
                      {item.fixed_by_participant && (
                        <Text className="text-xs text-purple-600 mt-1 italic">
                          Fixé par:{" "}
                          {item.fixed_by_participant.name_display ||
                            "Participant inconnu"}
                        </Text>
                      )}
                      {item.assigned_participant_id && (
                        <Text className="text-xs text-green-600 mt-1 italic">
                          Attribué
                        </Text>
                      )}
                    </View>
                    <View className="flex-col items-end gap-2">
                      {isOrganizer && (
                        <Dialog
                          open={
                            showItemFormDialog && editingItem?.id === item.id
                          }
                          onOpenChange={handleOpenChange}
                        >
                          <DialogTrigger asChild>
                            <Pressable
                              onPress={() => handleOpenEditModal(item)}
                            >
                              <Text className="text-blue-600 text-lg">✏️</Text>
                            </Pressable>
                          </DialogTrigger>
                          <DialogContent className="p-0 max-w-md">
                            <DialogTitle className="sr-only">
                              Modifier l'Item
                            </DialogTitle>
                            <DialogDescription className="sr-only">
                              Formulaire pour modifier un item existant
                            </DialogDescription>
                            <AddItemForm
                              eventId={eventId}
                              itemToEdit={editingItem} // Pass editing item state
                              onClose={handleCloseModal} // Use close handler
                              onItemSaved={() => {
                                loadData();
                                handleCloseModal();
                              }}
                            />
                          </DialogContent>
                        </Dialog>
                      )}
                      {isOrganizer && (
                        <Pressable
                          onPress={() => handleDeleteItem(item.id, item.name)}
                        >
                          <Text className="text-destructive text-lg">🗑️</Text>
                        </Pressable>
                      )}
                      {!isOrganizer && allowPreAssignment && (
                        <>
                          {canFix && (
                            <Button
                              variant="outline"
                              size="sm"
                              onPress={() => handleFixItem(item)}
                            >
                              <Text className="mr-1 text-primary">📌</Text>
                              <Text>Fixer</Text>
                            </Button>
                          )}
                          {isFixedByMe && (
                            <Button
                              variant="outline"
                              size="sm"
                              onPress={() => handleUnfixItem(item)}
                            >
                              <Text className="mr-1 text-destructive">✅</Text>
                              <Text>Annuler Fix</Text>
                            </Button>
                          )}
                          {fixedBySomeoneElse && (
                            <Button variant="outline" size="sm" disabled={true}>
                              <Text className="mr-1 text-muted-foreground">
                                ✅
                              </Text>
                              <Text className="text-muted-foreground">
                                Fixé
                              </Text>
                            </Button>
                          )}
                        </>
                      )}
                    </View>
                  </View>
                </View>
              );
            }}
            ItemSeparatorComponent={() => <Separator className="my-0" />}
          />
        )}
      </CardContent>
    </Card>
  );
}
