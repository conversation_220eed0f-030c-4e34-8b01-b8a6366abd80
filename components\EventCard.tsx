// --- Begin components/EventCard.tsx ---
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "~/components/ui/card";
import { Pressable, Text, View } from "react-native";
import { AspectRatio } from "./ui/aspect-ratio";
// Correction: Importer ParticipantStatus
import { EventWithParticipantStatus, ParticipantStatus } from "~/lib/types";
import { Link } from "expo-router";
import { cn } from "~/lib/utils";
import { Badge } from "./ui/badge";
// Icônes temporairement supprimées pour compatibilité web

interface EventCardProps {
  event: EventWithParticipantStatus;
  isOrganizer: boolean;
  isPast: boolean;
}

export function EventCard({ event, isOrganizer, isPast }: EventCardProps) {
  const {
    id,
    title,
    date_time,
    icon,
    location,
    participant_status,
    participant_role,
  } = event;

  const eventDate = new Date(date_time);
  const formattedDate = eventDate.toLocaleDateString("fr-FR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  let statusBadge = null;
  if (!isOrganizer && participant_status) {
    switch (participant_status) {
      case ParticipantStatus.Pending: // Utilise l'enum importé
        statusBadge = (
          <Badge
            variant="outline"
            className="ml-auto bg-yellow-100 border-yellow-300"
          >
            <Text className="text-yellow-800">En attente</Text>
          </Badge>
        );
        break;
      case ParticipantStatus.Maybe: // Utilise l'enum importé
        statusBadge = (
          <Badge
            variant="outline"
            className="ml-auto bg-orange-100 border-orange-300"
          >
            <Text className="text-orange-800">Peut-être</Text>
          </Badge>
        );
        break;
    }
  }

  // Fonction pour créer une URL conviviale à partir du titre
  const createSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // Supprimer les caractères spéciaux
      .replace(/\s+/g, "-") // Remplacer les espaces par des tirets
      .replace(/--+/g, "-") // Éviter les tirets multiples
      .trim(); // Supprimer les espaces au début et à la fin
  };

  return (
    <Link
      href={{
        pathname: `/event/${id}`,
        params: { title: createSlug(title) },
      }}
      asChild
    >
      <Pressable className="mb-2 group">
        <Card
          className={cn(
            "w-full flex-row group-active:bg-muted overflow-hidden border",
            isPast ? "opacity-60 border-border/50" : "border-border",
            isOrganizer ? "border-l-4 border-l-primary" : ""
          )}
        >
          <AspectRatio
            ratio={1}
            className="flex items-center justify-center w-20 h-auto bg-muted/50"
          >
            <Text className="text-4xl leading-tight">{icon || "🎉"}</Text>
          </AspectRatio>
          <View className="flex-1 justify-center p-3">
            <View className="flex-row items-center justify-between">
              <CardTitle
                className="text-lg font-semibold text-foreground mb-1 flex-shrink mr-2"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {title}
              </CardTitle>
              {isOrganizer && (
                <Badge variant="secondary" className="ml-auto">
                  <Text>Organisateur</Text>
                </Badge>
              )}
              {statusBadge}
            </View>
            <Text
              className="text-sm text-muted-foreground"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {formattedDate} à {formattedTime}
            </Text>
            {location && (
              <Text
                className="text-sm text-muted-foreground mt-1"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {location}
              </Text>
            )}
          </View>
        </Card>
      </Pressable>
    </Link>
  );
}
// --- End components/EventCard.tsx ---
