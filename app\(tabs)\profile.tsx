import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
} from "react-native";

// Context and Auth
import { useAuth } from "~/lib/AuthContext";
import CreateAccountForm from "~/components/CreateAccountForm";
import SignInForm from "~/components/SignInForm";

// UI Components
import { Text } from "~/components/ui/text";
import { H1, H3, Muted } from "~/components/ui/typography";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Switch } from "~/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertDialogHead<PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";

import { Separator } from "~/components/ui/separator";

// Lucide Icons
// Icônes temporairement supprimées pour compatibilité web

// Supabase and App Logic
import { fetchProfile, updateProfile } from "~/lib/supabaseCrud";
import { Profile } from "~/lib/types";
import { showToast } from "~/lib/toastService";

// Enum for auth view state
enum AuthView {
  NONE,
  CREATE_ACCOUNT,
  SIGN_IN,
}

export default function ProfileScreen() {
  const { session, isAnonymous, signOut: contextSignOut } = useAuth();
  const authUser = session?.user;
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);
  const [name, setName] = useState("");
  const [avatarUrl, setAvatarUrl] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isDiscoverable, setIsDiscoverable] = useState(false);
  const [currentAuthView, setCurrentAuthView] = useState<AuthView>(
    AuthView.NONE
  );

  // Load profile if session exists - RESTORED
  useEffect(() => {
    const loadProfileData = async () => {
      if (authUser?.id) {
        setLoading(true);
        try {
          const fetchedProfile = await fetchProfile(authUser.id);
          if (fetchedProfile) {
            setProfile(fetchedProfile);
            setName(fetchedProfile.name || "");
            setAvatarUrl(fetchedProfile.avatar_url || "");
            // Ensure is_discoverable is handled, assuming it's in fetchedProfile type
            setIsDiscoverable(fetchedProfile.is_discoverable || false);
          }
        } catch (error: any) {
          showToast(error.message || "Impossible de charger le profil.", {
            type: "error",
          });
        } finally {
          setLoading(false);
        }
      } else {
        setProfile(null);
        setCurrentAuthView(AuthView.NONE);
      }
    };
    loadProfileData();
  }, [authUser]); // Removed loadProfileData from dep array as it's defined inside

  // Platform-specific press handling - RESTORED
  const handlePress = (callback: () => void) => {
    return Platform.OS === "web"
      ? { onClick: callback }
      : { onPress: callback };
  };

  // handleUpdateProfile - RESTORED
  const handleUpdateProfile = async () => {
    if (!authUser?.id) return;
    setLoading(true);
    const updates: Partial<Profile> = {
      // Use Partial<Profile> for updates
      // id: authUser.id, // id is usually not part of update payload for RLS, PK is used in query
      name: name,
      avatar_url: avatarUrl,
      is_discoverable: isDiscoverable,
    };
    try {
      const updatedProfileData = await updateProfile(authUser.id, updates);
      if (updatedProfileData) {
        setProfile(updatedProfileData);
        showToast("Profil mis à jour !", { type: "success" });
        setIsEditing(false);
      } else {
        throw new Error("La mise à jour du profil a échoué.");
      }
    } catch (error: any) {
      showToast(error.message || "Impossible de mettre à jour le profil.", {
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // handleSignOut - RESTORED
  const handleSignOut = async () => {
    setAuthLoading(true);
    await contextSignOut();
    setAuthLoading(false);
    setProfile(null);
    setCurrentAuthView(AuthView.NONE);
    setIsEditing(false);
    showToast("Déconnexion réussie.", { type: "info" });
  };

  // handleDeleteAccount - RESTORED
  const handleDeleteAccount = () => {
    showToast("La suppression de compte sera bientôt disponible.", {
      type: "info",
    });
  };

  // Affichage pour les utilisateurs non connectés (ne devrait jamais arriver avec la connexion anonyme)
  if (!session) {
    return (
      <View className="flex-1 bg-background">
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{
            padding: 16,
            justifyContent: "center",
            minHeight: "100%",
          }}
        >
          <View className="items-center gap-6">
            <View className="bg-muted/20 p-6 rounded-full">
              <Text className="text-4xl">👤</Text>
            </View>

            <View className="items-center gap-2">
              <H1 className="text-2xl font-bold text-foreground">Bienvenue</H1>
              <Text className="text-muted-foreground text-center max-w-sm">
                Connectez-vous pour accéder à votre profil et gérer vos
                événements
              </Text>
            </View>

            <View className="w-full max-w-sm gap-4">
              {currentAuthView === AuthView.NONE && (
                <>
                  <Button
                    onPress={() => setCurrentAuthView(AuthView.SIGN_IN)}
                    className="w-full"
                  >
                    <Text>🔑 Se connecter</Text>
                  </Button>
                  <Button
                    variant="outline"
                    onPress={() => setCurrentAuthView(AuthView.CREATE_ACCOUNT)}
                    className="w-full"
                  >
                    <Text>👤 Créer un compte</Text>
                  </Button>
                </>
              )}

              {currentAuthView === AuthView.SIGN_IN && (
                <Card className="border border-border">
                  <CardHeader>
                    <CardTitle>
                      <Text>Connexion</Text>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <SignInForm />
                    <Button
                      variant="ghost"
                      onPress={() => setCurrentAuthView(AuthView.NONE)}
                      className="mt-4 w-full"
                    >
                      <Text>Retour</Text>
                    </Button>
                  </CardContent>
                </Card>
              )}

              {currentAuthView === AuthView.CREATE_ACCOUNT && (
                <Card className="border border-border">
                  <CardHeader>
                    <CardTitle>
                      <Text>Créer un compte</Text>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CreateAccountForm />
                    <Button
                      variant="ghost"
                      onPress={() => setCurrentAuthView(AuthView.NONE)}
                      className="mt-4 w-full"
                    >
                      <Text>Retour</Text>
                    </Button>
                  </CardContent>
                </Card>
              )}
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  // Affichage pour les utilisateurs connectés (y compris anonymes)
  return (
    <View className="flex-1 bg-background">
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
        <View className="gap-6">
          {/* Message pour les utilisateurs anonymes */}
          {isAnonymous && (
            <Card className="border border-orange-200 bg-orange-50">
              <CardHeader>
                <CardTitle className="flex-row items-center gap-2">
                  <Text>👋</Text>
                  <Text>Mode invité</Text>
                </CardTitle>
                <CardDescription>
                  <Text>
                    Vous utilisez l'application en mode invité. Créez un compte
                    pour sauvegarder vos données et accéder à toutes les
                    fonctionnalités.
                  </Text>
                </CardDescription>
              </CardHeader>
              <CardContent className="gap-3">
                <Button
                  onPress={() => setCurrentAuthView(AuthView.CREATE_ACCOUNT)}
                  className="w-full"
                >
                  <Text>👤 Créer un compte</Text>
                </Button>
                <Button
                  variant="outline"
                  onPress={() => setCurrentAuthView(AuthView.SIGN_IN)}
                  className="w-full"
                >
                  <Text>🔑 J'ai déjà un compte</Text>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Formulaires d'authentification pour les utilisateurs anonymes */}
          {isAnonymous && currentAuthView === AuthView.SIGN_IN && (
            <Card className="border border-border">
              <CardHeader>
                <CardTitle>
                  <Text>Connexion</Text>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SignInForm />
                <Button
                  variant="ghost"
                  onPress={() => setCurrentAuthView(AuthView.NONE)}
                  className="mt-4 w-full"
                >
                  <Text>Retour</Text>
                </Button>
              </CardContent>
            </Card>
          )}

          {isAnonymous && currentAuthView === AuthView.CREATE_ACCOUNT && (
            <Card className="border border-border">
              <CardHeader>
                <CardTitle>
                  <Text>Créer un compte</Text>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CreateAccountForm />
                <Button
                  variant="ghost"
                  onPress={() => setCurrentAuthView(AuthView.NONE)}
                  className="mt-4 w-full"
                >
                  <Text>Retour</Text>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Header avec avatar et infos de base */}
          <Card className="border border-border">
            <CardHeader className="items-center pb-4">
              <View className="w-20 h-20 mb-4 bg-muted rounded-full items-center justify-center">
                <Text className="text-4xl">👤</Text>
              </View>
              <View className="items-center gap-1">
                <H1 className="text-xl font-bold text-foreground">
                  {isAnonymous
                    ? "Utilisateur invité"
                    : profile?.name || "Utilisateur"}
                </H1>
                <View className="flex-row items-center gap-2">
                  <Text className="text-sm">{isAnonymous ? "👤" : "📧"}</Text>
                  <Text className="text-sm text-muted-foreground">
                    {isAnonymous ? "Mode invité" : session.user.email}
                  </Text>
                </View>
              </View>
              {!isAnonymous && (
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => setIsEditing(!isEditing)}
                  className="mt-2"
                >
                  <Text>{isEditing ? "❌ Annuler" : "✏️ Modifier"}</Text>
                </Button>
              )}
            </CardHeader>
          </Card>

          {/* Formulaire d'édition */}
          {!isAnonymous && isEditing && (
            <Card className="border border-border">
              <CardHeader>
                <CardTitle className="flex-row items-center gap-2">
                  <Text>⚙️</Text>
                  <Text>Modifier le profil</Text>
                </CardTitle>
              </CardHeader>
              <CardContent className="gap-4">
                <View>
                  <Label nativeID="nameLabel" className="mb-2">
                    Nom d'affichage
                  </Label>
                  <Input
                    nativeID="nameLabel"
                    value={name}
                    onChangeText={setName}
                    placeholder="Votre nom"
                    className="h-11"
                  />
                </View>

                <View>
                  <Label nativeID="avatarLabel" className="mb-2">
                    URL de l'avatar (optionnel)
                  </Label>
                  <Input
                    nativeID="avatarLabel"
                    value={avatarUrl}
                    onChangeText={setAvatarUrl}
                    placeholder="https://..."
                    className="h-11"
                  />
                </View>

                <View className="flex-row items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <View className="flex-1">
                    <Label nativeID="discoverableLabel" className="font-medium">
                      Profil découvrable
                    </Label>
                    <Text className="text-sm text-muted-foreground mt-1">
                      Permettre aux autres utilisateurs de vous trouver
                    </Text>
                  </View>
                  <Switch
                    nativeID="discoverableLabel"
                    checked={isDiscoverable}
                    onCheckedChange={setIsDiscoverable}
                  />
                </View>

                <View className="flex-row gap-3 mt-4">
                  <Button
                    onPress={handleUpdateProfile}
                    disabled={loading}
                    className="flex-1"
                  >
                    {loading ? (
                      <ActivityIndicator size="small" color="#ffffff" />
                    ) : (
                      <Text>💾 Sauvegarder</Text>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onPress={() => setIsEditing(false)}
                    className="flex-1"
                  >
                    <Text>Annuler</Text>
                  </Button>
                </View>
              </CardContent>
            </Card>
          )}

          {/* Statistiques */}
          <Card className="border border-border">
            <CardHeader>
              <CardTitle className="flex-row items-center gap-2">
                <Text>📊</Text>
                <Text>Mes statistiques</Text>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <View className="flex-row justify-around">
                <View className="items-center">
                  <Text className="text-2xl font-bold text-foreground">0</Text>
                  <Text className="text-sm text-muted-foreground">
                    Événements créés
                  </Text>
                </View>
                <Separator orientation="vertical" className="h-12" />
                <View className="items-center">
                  <Text className="text-2xl font-bold text-foreground">0</Text>
                  <Text className="text-sm text-muted-foreground">
                    Participations
                  </Text>
                </View>
                <Separator orientation="vertical" className="h-12" />
                <View className="items-center">
                  <Text className="text-2xl font-bold text-foreground">0</Text>
                  <Text className="text-sm text-muted-foreground">
                    Tâches complétées
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card className="border border-border">
            <CardHeader>
              <CardTitle>
                <Text>Actions</Text>
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              <Button
                variant="outline"
                onPress={() =>
                  Alert.alert("Notifications", "Fonctionnalité à venir")
                }
                className="w-full justify-start"
              >
                <Text>🔔 Paramètres de notification</Text>
              </Button>

              <Button
                variant="outline"
                onPress={() =>
                  Alert.alert("Confidentialité", "Fonctionnalité à venir")
                }
                className="w-full justify-start"
              >
                <Text>🛡️ Confidentialité et sécurité</Text>
              </Button>

              {/* Bouton de déconnexion seulement pour les utilisateurs authentifiés */}
              {!isAnonymous && (
                <>
                  <Separator />

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        className="w-full justify-start"
                      >
                        <Text>🚪 Se déconnecter</Text>
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          <Text>Confirmer la déconnexion</Text>
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          <Text>
                            Êtes-vous sûr de vouloir vous déconnecter ?
                          </Text>
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>
                          <Text>Annuler</Text>
                        </AlertDialogCancel>
                        <AlertDialogAction onPress={handleSignOut}>
                          <Text>Se déconnecter</Text>
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </>
              )}
            </CardContent>
          </Card>

          {/* Espace en bas pour éviter que le contenu soit coupé */}
          <View className="h-8" />
        </View>
      </ScrollView>
    </View>
  );
}
